{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/ui": "^2.18.6", "@nuxtjs/device": "^3.2.4", "@prisma/nuxt": "^0.3.0", "@vuepic/vue-datepicker": "^9.0.3", "numeral": "^2.0.6", "nuxt": "^3.13.0", "sweetalert2": "^11.14.5", "uuid": "^11.0.5", "v-calendar": "^3.1.2", "vue": "latest", "vue-router": "latest"}}