generator client {
  provider = "prisma-client-js"
  output   = "../prisma/generated/pcr-funeral-dev"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model job_lines {
  id           Int     @id @default(autoincrement())
  job_id       Int
  user_id      Int
  user_name    String?
  user_running Int?
  user_address String?
  user_phone   String?
  user_alias   String?
  isPaid       Boolean @default(false)
  imageGuid    String?
  jobs         jobs    @relation(fields: [job_id], references: [id])
}

model jobs {
  id            Int         @id @default(autoincrement())
  job_name      String      @default("")
  age           Int
  owner_id      Int
  owner_name    String?
  owner_running Int?
  date          DateTime?
  prefix        String?
  job_lines     job_lines[]
}

model seats {
  id   Int     @id @default(autoincrement())
  host String  @unique
  hash String?
}

model users {
  id       Int          @id @default(autoincrement())
  username String?
  password String?
  name     String?
  address  String?
  phone    String?
  running  Int?
  isActive Boolean      @default(true)
  alias    String?
  type     userTypeEnum @default(MEMBER)
}

enum userTypeEnum {
  MEMBER
  USER
}
