// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2024-04-03',
  ssr: true,
  devtools: { enabled: true },
  modules: [
    '@nuxt/ui',
    '@prisma/nuxt'
  ],
  sourcemap: true,
  runtimeConfig:{
    //BEARER_TOKEN: '$2a$12$rcUzVifOUcSYFKowpXAf0Oc7.IN/IabK3yVGLSvzJ.bXWtOaTIbwe',
    public: {
      BASE_API: 'http://localhost:8000',
      BEARER_TOKEN: process.env.BEARER_TOKEN,
    },
  }
})